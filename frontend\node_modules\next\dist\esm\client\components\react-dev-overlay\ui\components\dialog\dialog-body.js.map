{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/dialog/dialog-body.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport type DialogBodyProps = {\n  children?: React.ReactNode\n  className?: string\n}\n\nconst DialogBody: React.FC<DialogBodyProps> = function DialogBody({\n  children,\n  className,\n}) {\n  return (\n    <div data-nextjs-dialog-body className={className}>\n      {children}\n    </div>\n  )\n}\n\nexport { DialogBody }\n"], "names": ["React", "DialogBody", "children", "className", "div", "data-nextjs-dialog-body"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAO9B,MAAMC,aAAwC,SAASA,WAAW,KAGjE;IAHiE,IAAA,EAChEC,QAAQ,EACRC,SAAS,EACV,GAHiE;IAIhE,qBACE,KAACC;QAAIC,yBAAuB;QAACF,WAAWA;kBACrCD;;AAGP;AAEA,SAASD,UAAU,GAAE"}
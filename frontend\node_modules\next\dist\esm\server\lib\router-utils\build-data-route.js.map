{"version": 3, "sources": ["../../../../src/server/lib/router-utils/build-data-route.ts"], "sourcesContent": ["import path from '../../../shared/lib/isomorphic/path'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\nimport { isDynamicRoute } from '../../../shared/lib/router/utils/is-dynamic'\nimport { getNamedRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { normalizeRouteRegex } from '../../../lib/load-custom-routes'\nimport { escapeStringRegexp } from '../../../shared/lib/escape-regexp'\n\nexport function buildDataRoute(page: string, buildId: string) {\n  const pagePath = normalizePagePath(page)\n  const dataRoute = path.posix.join('/_next/data', buildId, `${pagePath}.json`)\n\n  let dataRouteRegex: string\n  let namedDataRouteRegex: string | undefined\n  let routeKeys: { [named: string]: string } | undefined\n\n  if (isDynamicRoute(page)) {\n    const routeRegex = getNamedRouteRegex(dataRoute, {\n      prefixRouteKeys: true,\n      includeSuffix: true,\n      excludeOptionalTrailingSlash: true,\n    })\n\n    dataRouteRegex = normalizeRouteRegex(routeRegex.re.source)\n    namedDataRouteRegex = routeRegex.namedRegex\n    routeKeys = routeRegex.routeKeys\n  } else {\n    dataRouteRegex = normalizeRouteRegex(\n      new RegExp(\n        `^${path.posix.join(\n          '/_next/data',\n          escapeStringRegexp(buildId),\n          `${pagePath}\\\\.json`\n        )}$`\n      ).source\n    )\n  }\n\n  return {\n    page,\n    routeKeys,\n    dataRouteRegex,\n    namedDataRouteRegex,\n  }\n}\n"], "names": ["path", "normalizePagePath", "isDynamicRoute", "getNamedRouteRegex", "normalizeRouteRegex", "escapeStringRegexp", "buildDataRoute", "page", "buildId", "pagePath", "dataRoute", "posix", "join", "dataRouteRegex", "namedDataRouteRegex", "routeKeys", "routeRegex", "prefixRouteKeys", "includeSuffix", "excludeOptionalTrailingSlash", "re", "source", "namedRegex", "RegExp"], "mappings": "AAAA,OAAOA,UAAU,sCAAqC;AACtD,SAASC,iBAAiB,QAAQ,oDAAmD;AACrF,SAASC,cAAc,QAAQ,8CAA6C;AAC5E,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,mBAAmB,QAAQ,kCAAiC;AACrE,SAASC,kBAAkB,QAAQ,oCAAmC;AAEtE,OAAO,SAASC,eAAeC,IAAY,EAAEC,OAAe;IAC1D,MAAMC,WAAWR,kBAAkBM;IACnC,MAAMG,YAAYV,KAAKW,KAAK,CAACC,IAAI,CAAC,eAAeJ,SAAS,GAAGC,SAAS,KAAK,CAAC;IAE5E,IAAII;IACJ,IAAIC;IACJ,IAAIC;IAEJ,IAAIb,eAAeK,OAAO;QACxB,MAAMS,aAAab,mBAAmBO,WAAW;YAC/CO,iBAAiB;YACjBC,eAAe;YACfC,8BAA8B;QAChC;QAEAN,iBAAiBT,oBAAoBY,WAAWI,EAAE,CAACC,MAAM;QACzDP,sBAAsBE,WAAWM,UAAU;QAC3CP,YAAYC,WAAWD,SAAS;IAClC,OAAO;QACLF,iBAAiBT,oBACf,IAAImB,OACF,CAAC,CAAC,EAAEvB,KAAKW,KAAK,CAACC,IAAI,CACjB,eACAP,mBAAmBG,UACnB,GAAGC,SAAS,OAAO,CAAC,EACpB,CAAC,CAAC,EACJY,MAAM;IAEZ;IAEA,OAAO;QACLd;QACAQ;QACAF;QACAC;IACF;AACF"}
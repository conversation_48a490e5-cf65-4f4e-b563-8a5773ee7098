{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.tsx"], "sourcesContent": ["import { useMemo, useState } from 'react'\nimport { CollapseIcon } from '../../icons/collapse-icon'\n/**\n *\n * Format component stack into pseudo HTML\n * component stack is an array of strings, e.g.: ['p', 'p', 'Page', ...]\n *\n * For html tags mismatch, it will render it for the code block\n *\n * ```\n * <pre>\n *  <code>{`\n *    <Page>\n *       <p red>\n *         <p red>\n *  `}</code>\n * </pre>\n * ```\n *\n * For text mismatch, it will render it for the code block\n *\n * ```\n * <pre>\n * <code>{`\n *   <Page>\n *     <p>\n *       \"Server Text\" (green)\n *       \"Client Text\" (red)\n *     </p>\n *   </Page>\n * `}</code>\n * ```\n *\n * For bad text under a tag it will render it for the code block,\n * e.g. \"Mismatched Text\" under <p>\n *\n * ```\n * <pre>\n * <code>{`\n *   <Page>\n *     <div>\n *       <p>\n *         \"Mismatched Text\" (red)\n *      </p>\n *     </div>\n *   </Page>\n * `}</code>\n * ```\n *\n */\nexport function PseudoHtmlDiff({\n  firstContent,\n  secondContent,\n  hydrationMismatchType,\n  reactOutputComponentDiff,\n  ...props\n}: {\n  firstContent: string\n  secondContent: string\n  reactOutputComponentDiff: string\n  hydrationMismatchType: 'tag' | 'text' | 'text-in-tag'\n} & React.HTMLAttributes<HTMLPreElement>) {\n  const [isDiffCollapsed, toggleCollapseHtml] = useState(true)\n\n  const htmlComponents = useMemo(() => {\n    const componentStacks: React.ReactNode[] = []\n    const reactComponentDiffLines = reactOutputComponentDiff!.split('\\n')\n    reactComponentDiffLines.forEach((line, index) => {\n      const isDiffLine = line[0] === '+' || line[0] === '-'\n      const isHighlightedLine = line[0] === '>'\n      const hasSign = isDiffLine || isHighlightedLine\n      const sign = hasSign ? line[0] : ''\n      const signIndex = hasSign ? line.indexOf(sign) : -1\n      const [prefix, suffix] = hasSign\n        ? [line.slice(0, signIndex), line.slice(signIndex + 1)]\n        : [line, '']\n\n      if (isDiffLine) {\n        componentStacks.push(\n          <span\n            key={'comp-diff' + index}\n            data-nextjs-container-errors-pseudo-html-line\n            data-nextjs-container-errors-pseudo-html--diff={\n              sign === '+' ? 'add' : 'remove'\n            }\n          >\n            <span>\n              {/* Slice 2 spaces for the icon */}\n              {prefix}\n              <span data-nextjs-container-errors-pseudo-html-line-sign>\n                {sign}\n              </span>\n              {suffix}\n              {'\\n'}\n            </span>\n          </span>\n        )\n      } else {\n        // In general, if it's not collapsed, show the whole diff\n        componentStacks.push(\n          <span\n            data-nextjs-container-errors-pseudo-html-line\n            key={'comp-diff' + index}\n            {...(isHighlightedLine\n              ? {\n                  'data-nextjs-container-errors-pseudo-html--diff': 'error',\n                }\n              : undefined)}\n          >\n            {prefix}\n            <span data-nextjs-container-errors-pseudo-html-line-sign>\n              {sign}\n            </span>\n            {suffix}\n            {'\\n'}\n          </span>\n        )\n      }\n    })\n    return componentStacks\n  }, [reactOutputComponentDiff])\n\n  return (\n    <div\n      data-nextjs-container-errors-pseudo-html\n      data-nextjs-container-errors-pseudo-html-collapse={isDiffCollapsed}\n    >\n      <button\n        tabIndex={10} // match CallStackFrame\n        data-nextjs-container-errors-pseudo-html-collapse-button\n        onClick={() => toggleCollapseHtml(!isDiffCollapsed)}\n      >\n        <CollapseIcon collapsed={isDiffCollapsed} />\n      </button>\n      <pre {...props}>\n        <code>{htmlComponents}</code>\n      </pre>\n    </div>\n  )\n}\n"], "names": ["useMemo", "useState", "CollapseIcon", "PseudoHtmlDiff", "firstContent", "second<PERSON><PERSON>nt", "hydrationMismatchType", "reactOutputComponentDiff", "props", "isDiffCollapsed", "toggleCollapseHtml", "htmlComponents", "componentStacks", "reactComponentDiffLines", "split", "for<PERSON>ach", "line", "index", "isDiffLine", "isHighlightedLine", "hasSign", "sign", "signIndex", "indexOf", "prefix", "suffix", "slice", "push", "span", "data-nextjs-container-errors-pseudo-html-line", "data-nextjs-container-errors-pseudo-html--diff", "data-nextjs-container-errors-pseudo-html-line-sign", "undefined", "div", "data-nextjs-container-errors-pseudo-html", "data-nextjs-container-errors-pseudo-html-collapse", "button", "tabIndex", "data-nextjs-container-errors-pseudo-html-collapse-button", "onClick", "collapsed", "pre", "code"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,QAAQ,QAAQ,QAAO;AACzC,SAASC,YAAY,QAAQ,4BAA2B;AACxD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CC,GACD,OAAO,SAASC,eAAe,KAWS;IAXT,IAAA,EAC7BC,YAAY,EACZC,aAAa,EACbC,qBAAqB,EACrBC,wBAAwB,EACxB,GAAGC,OAMmC,GAXT;IAY7B,MAAM,CAACC,iBAAiBC,mBAAmB,GAAGT,SAAS;IAEvD,MAAMU,iBAAiBX,QAAQ;QAC7B,MAAMY,kBAAqC,EAAE;QAC7C,MAAMC,0BAA0BN,yBAA0BO,KAAK,CAAC;QAChED,wBAAwBE,OAAO,CAAC,CAACC,MAAMC;YACrC,MAAMC,aAAaF,IAAI,CAAC,EAAE,KAAK,OAAOA,IAAI,CAAC,EAAE,KAAK;YAClD,MAAMG,oBAAoBH,IAAI,CAAC,EAAE,KAAK;YACtC,MAAMI,UAAUF,cAAcC;YAC9B,MAAME,OAAOD,UAAUJ,IAAI,CAAC,EAAE,GAAG;YACjC,MAAMM,YAAYF,UAAUJ,KAAKO,OAAO,CAACF,QAAQ,CAAC;YAClD,MAAM,CAACG,QAAQC,OAAO,GAAGL,UACrB;gBAACJ,KAAKU,KAAK,CAAC,GAAGJ;gBAAYN,KAAKU,KAAK,CAACJ,YAAY;aAAG,GACrD;gBAACN;gBAAM;aAAG;YAEd,IAAIE,YAAY;gBACdN,gBAAgBe,IAAI,eAClB,KAACC;oBAECC,+CAA6C;oBAC7CC,kDACET,SAAS,MAAM,QAAQ;8BAGzB,cAAA,MAACO;;4BAEEJ;0CACD,KAACI;gCAAKG,oDAAkD;0CACrDV;;4BAEFI;4BACA;;;mBAbE,cAAcR;YAiBzB,OAAO;gBACL,yDAAyD;gBACzDL,gBAAgBe,IAAI,eAClB,MAACC;oBACCC,+CAA6C;oBAE5C,GAAIV,oBACD;wBACE,kDAAkD;oBACpD,IACAa,SAAS;;wBAEZR;sCACD,KAACI;4BAAKG,oDAAkD;sCACrDV;;wBAEFI;wBACA;;mBAZI,cAAcR;YAezB;QACF;QACA,OAAOL;IACT,GAAG;QAACL;KAAyB;IAE7B,qBACE,MAAC0B;QACCC,0CAAwC;QACxCC,qDAAmD1B;;0BAEnD,KAAC2B;gBACCC,UAAU;gBACVC,0DAAwD;gBACxDC,SAAS,IAAM7B,mBAAmB,CAACD;0BAEnC,cAAA,KAACP;oBAAasC,WAAW/B;;;0BAE3B,KAACgC;gBAAK,GAAGjC,KAAK;0BACZ,cAAA,KAACkC;8BAAM/B;;;;;AAIf"}
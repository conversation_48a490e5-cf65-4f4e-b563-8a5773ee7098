{"version": 3, "sources": ["../../../../src/server/lib/router-utils/decode-path-params.ts"], "sourcesContent": ["import escapePathDelimiters from '../../../shared/lib/router/utils/escape-path-delimiters'\nimport { DecodeError } from '../../../shared/lib/utils'\n\n/**\n * We only encode path delimiters for path segments from\n * getStaticPaths so we need to attempt decoding the URL\n * to match against and only escape the path delimiters\n * this allows non-ascii values to be handled e.g.\n * Japanese characters.\n * */\nfunction decodePathParams(pathname: string): string {\n  // TODO: investigate adding this handling for non-SSG\n  // pages so non-ascii names also work there.\n  return pathname\n    .split('/')\n    .map((seg) => {\n      try {\n        seg = escapePathDelimiters(decodeURIComponent(seg), true)\n      } catch (_) {\n        // An improperly encoded URL was provided\n        throw new DecodeError('Failed to decode path param(s).')\n      }\n      return seg\n    })\n    .join('/')\n}\n\nexport { decodePathParams }\n"], "names": ["decodePathParams", "pathname", "split", "map", "seg", "escapePathDelimiters", "decodeURIComponent", "_", "DecodeError", "join"], "mappings": ";;;;+BA2BSA;;;eAAAA;;;6EA3BwB;uBACL;;;;;;AAE5B;;;;;;GAMG,GACH,SAASA,iBAAiBC,QAAgB;IACxC,qDAAqD;IACrD,4CAA4C;IAC5C,OAAOA,SACJC,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC;QACJ,IAAI;YACFA,MAAMC,IAAAA,6BAAoB,EAACC,mBAAmBF,MAAM;QACtD,EAAE,OAAOG,GAAG;YACV,yCAAyC;YACzC,MAAM,qBAAkD,CAAlD,IAAIC,kBAAW,CAAC,oCAAhB,qBAAA;uBAAA;4BAAA;8BAAA;YAAiD;QACzD;QACA,OAAOJ;IACT,GACCK,IAAI,CAAC;AACV"}
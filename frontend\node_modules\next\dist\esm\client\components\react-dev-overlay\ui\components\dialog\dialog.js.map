{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/dialog/dialog.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { useOnClickOutside } from '../../hooks/use-on-click-outside'\nimport { useMeasureHeight } from '../../hooks/use-measure-height'\n\nexport type DialogProps = {\n  children?: React.ReactNode\n  type: 'error' | 'warning'\n  'aria-labelledby': string\n  'aria-describedby': string\n  className?: string\n  onClose?: () => void\n  dialogResizerRef?: React.RefObject<HTMLDivElement | null>\n} & React.HTMLAttributes<HTMLDivElement>\n\nconst CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE = [\n  '[data-next-mark]',\n  '[data-issues-open]',\n  '#nextjs-dev-tools-menu',\n  '[data-nextjs-error-overlay-nav]',\n  '[data-info-popover]',\n]\n\nconst Dialog: React.FC<DialogProps> = function Dialog({\n  children,\n  type,\n  className,\n  onClose,\n  'aria-labelledby': ariaLabelledBy,\n  'aria-describedby': ariaDescribedBy,\n  dialogResizerRef,\n  ...props\n}) {\n  const dialogRef = React.useRef<HTMLDivElement | null>(null)\n  const [role, setRole] = React.useState<string | undefined>(\n    typeof document !== 'undefined' && document.hasFocus()\n      ? 'dialog'\n      : undefined\n  )\n\n  const ref = React.useRef<HTMLDivElement | null>(null)\n  const [height, pristine] = useMeasureHeight(ref)\n\n  useOnClickOutside(\n    dialogRef.current,\n    CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE,\n    (e) => {\n      e.preventDefault()\n      return onClose?.()\n    }\n  )\n\n  React.useEffect(() => {\n    if (dialogRef.current == null) {\n      return\n    }\n\n    function handleFocus() {\n      // safari will force itself as the active application when a background page triggers any sort of autofocus\n      // this is a workaround to only set the dialog role if the document has focus\n      setRole(document.hasFocus() ? 'dialog' : undefined)\n    }\n\n    window.addEventListener('focus', handleFocus)\n    window.addEventListener('blur', handleFocus)\n    return () => {\n      window.removeEventListener('focus', handleFocus)\n      window.removeEventListener('blur', handleFocus)\n    }\n  }, [])\n\n  React.useEffect(() => {\n    const dialog = dialogRef.current\n    const root = dialog?.getRootNode()\n    const initialActiveElement =\n      root instanceof ShadowRoot ? (root?.activeElement as HTMLElement) : null\n\n    // Trap focus within the dialog\n    dialog?.focus()\n\n    return () => {\n      // Blur first to avoid getting stuck, in case `activeElement` is missing\n      dialog?.blur()\n      // Restore focus to the previously active element\n      initialActiveElement?.focus()\n    }\n  }, [])\n\n  return (\n    <div\n      ref={dialogRef}\n      tabIndex={-1}\n      data-nextjs-dialog\n      role={role}\n      aria-labelledby={ariaLabelledBy}\n      aria-describedby={ariaDescribedBy}\n      aria-modal=\"true\"\n      className={className}\n      onKeyDown={(e) => {\n        if (e.key === 'Escape') {\n          onClose?.()\n        }\n      }}\n      {...props}\n    >\n      <div\n        ref={dialogResizerRef}\n        data-nextjs-dialog-sizer\n        // [x] Don't animate on initial load\n        // [x] No duplicate elements\n        // [x] Responds to content growth\n        style={{\n          height,\n          transition: pristine ? undefined : 'height 250ms var(--timing-swift)',\n        }}\n      >\n        <div ref={ref}>{children}</div>\n      </div>\n    </div>\n  )\n}\n\nexport { Dialog }\n"], "names": ["React", "useOnClickOutside", "useMeasureHeight", "CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE", "Dialog", "children", "type", "className", "onClose", "ariaLabelledBy", "ariaDescribedBy", "dialogResizerRef", "props", "dialogRef", "useRef", "role", "setRole", "useState", "document", "hasFocus", "undefined", "ref", "height", "pristine", "current", "e", "preventDefault", "useEffect", "handleFocus", "window", "addEventListener", "removeEventListener", "dialog", "root", "getRootNode", "initialActiveElement", "ShadowRoot", "activeElement", "focus", "blur", "div", "tabIndex", "data-nextjs-dialog", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "aria-modal", "onKeyDown", "key", "data-nextjs-dialog-sizer", "style", "transition"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,iBAAiB,QAAQ,mCAAkC;AACpE,SAASC,gBAAgB,QAAQ,iCAAgC;AAYjE,MAAMC,4CAA4C;IAChD;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,SAAgC,SAASA,OAAO,KASrD;IATqD,IAAA,EACpDC,QAAQ,EACRC,IAAI,EACJC,SAAS,EACTC,OAAO,EACP,mBAAmBC,cAAc,EACjC,oBAAoBC,eAAe,EACnCC,gBAAgB,EAChB,GAAGC,OACJ,GATqD;IAUpD,MAAMC,YAAYb,MAAMc,MAAM,CAAwB;IACtD,MAAM,CAACC,MAAMC,QAAQ,GAAGhB,MAAMiB,QAAQ,CACpC,OAAOC,aAAa,eAAeA,SAASC,QAAQ,KAChD,WACAC;IAGN,MAAMC,MAAMrB,MAAMc,MAAM,CAAwB;IAChD,MAAM,CAACQ,QAAQC,SAAS,GAAGrB,iBAAiBmB;IAE5CpB,kBACEY,UAAUW,OAAO,EACjBrB,2CACA,CAACsB;QACCA,EAAEC,cAAc;QAChB,OAAOlB,2BAAAA;IACT;IAGFR,MAAM2B,SAAS,CAAC;QACd,IAAId,UAAUW,OAAO,IAAI,MAAM;YAC7B;QACF;QAEA,SAASI;YACP,2GAA2G;YAC3G,6EAA6E;YAC7EZ,QAAQE,SAASC,QAAQ,KAAK,WAAWC;QAC3C;QAEAS,OAAOC,gBAAgB,CAAC,SAASF;QACjCC,OAAOC,gBAAgB,CAAC,QAAQF;QAChC,OAAO;YACLC,OAAOE,mBAAmB,CAAC,SAASH;YACpCC,OAAOE,mBAAmB,CAAC,QAAQH;QACrC;IACF,GAAG,EAAE;IAEL5B,MAAM2B,SAAS,CAAC;QACd,MAAMK,SAASnB,UAAUW,OAAO;QAChC,MAAMS,OAAOD,0BAAAA,OAAQE,WAAW;QAChC,MAAMC,uBACJF,gBAAgBG,aAAcH,wBAAAA,KAAMI,aAAa,GAAmB;QAEtE,+BAA+B;QAC/BL,0BAAAA,OAAQM,KAAK;QAEb,OAAO;YACL,wEAAwE;YACxEN,0BAAAA,OAAQO,IAAI;YACZ,iDAAiD;YACjDJ,wCAAAA,qBAAsBG,KAAK;QAC7B;IACF,GAAG,EAAE;IAEL,qBACE,KAACE;QACCnB,KAAKR;QACL4B,UAAU,CAAC;QACXC,oBAAkB;QAClB3B,MAAMA;QACN4B,mBAAiBlC;QACjBmC,oBAAkBlC;QAClBmC,cAAW;QACXtC,WAAWA;QACXuC,WAAW,CAACrB;YACV,IAAIA,EAAEsB,GAAG,KAAK,UAAU;gBACtBvC,2BAAAA;YACF;QACF;QACC,GAAGI,KAAK;kBAET,cAAA,KAAC4B;YACCnB,KAAKV;YACLqC,0BAAwB;YACxB,oCAAoC;YACpC,4BAA4B;YAC5B,iCAAiC;YACjCC,OAAO;gBACL3B;gBACA4B,YAAY3B,WAAWH,YAAY;YACrC;sBAEA,cAAA,KAACoB;gBAAInB,KAAKA;0BAAMhB;;;;AAIxB;AAEA,SAASD,MAAM,GAAE"}
{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.tsx"], "sourcesContent": ["/*\n * Singleton store to track whether the app is currently being rendered\n * Used by the dev tools indicator to show render status\n */\n\nimport { useSyncExternalStore } from 'react'\n\nlet isVisible = false\nlet listeners: Array<() => void> = []\n\nconst subscribe = (listener: () => void) => {\n  listeners.push(listener)\n  return () => {\n    listeners = listeners.filter((l) => l !== listener)\n  }\n}\n\nconst getSnapshot = () => isVisible\n\nconst show = () => {\n  isVisible = true\n  listeners.forEach((listener) => listener())\n}\n\nconst hide = () => {\n  isVisible = false\n  listeners.forEach((listener) => listener())\n}\n\nexport function useIsDevRendering() {\n  return useSyncExternalStore(subscribe, getSnapshot)\n}\n\nexport const devRenderIndicator = {\n  show,\n  hide,\n}\n"], "names": ["useSyncExternalStore", "isVisible", "listeners", "subscribe", "listener", "push", "filter", "l", "getSnapshot", "show", "for<PERSON>ach", "hide", "useIsDevRendering", "devRenderIndicator"], "mappings": "AAAA;;;CAGC,GAED,SAASA,oBAAoB,QAAQ,QAAO;AAE5C,IAAIC,YAAY;AAChB,IAAIC,YAA+B,EAAE;AAErC,MAAMC,YAAY,CAACC;IACjBF,UAAUG,IAAI,CAACD;IACf,OAAO;QACLF,YAAYA,UAAUI,MAAM,CAAC,CAACC,IAAMA,MAAMH;IAC5C;AACF;AAEA,MAAMI,cAAc,IAAMP;AAE1B,MAAMQ,OAAO;IACXR,YAAY;IACZC,UAAUQ,OAAO,CAAC,CAACN,WAAaA;AAClC;AAEA,MAAMO,OAAO;IACXV,YAAY;IACZC,UAAUQ,OAAO,CAAC,CAACN,WAAaA;AAClC;AAEA,OAAO,SAASQ;IACd,OAAOZ,qBAAqBG,WAAWK;AACzC;AAEA,OAAO,MAAMK,qBAAqB;IAChCJ;IACAE;AACF,EAAC"}
{"version": 3, "sources": ["../../../../../src/server/route-modules/app-route/helpers/auto-implement-methods.ts"], "sourcesContent": ["import type { AppRouteHandlerFn, AppRouteHandlers } from '../module'\n\nimport { HTTP_METHODS, type HTTP_METHOD } from '../../../web/http'\n\nconst AUTOMATIC_ROUTE_METHODS = ['HEAD', 'OPTIONS'] as const\n\nfunction handleMethodNotAllowedResponse(): Response {\n  return new Response(null, { status: 405 })\n}\n\nexport function autoImplementMethods(\n  handlers: AppRouteHandlers\n): Record<HTTP_METHOD, AppRouteHandlerFn> {\n  // Loop through all the HTTP methods to create the initial methods object.\n  // Each of the methods will be set to the 405 response handler.\n  const methods: Record<HTTP_METHOD, AppRouteHandlerFn> = HTTP_METHODS.reduce(\n    (acc, method) => ({\n      ...acc,\n      // If the userland module implements the method, then use it. Otherwise,\n      // use the 405 response handler.\n      [method]: handlers[method] ?? handleMethodNotAllowedResponse,\n    }),\n    {} as Record<HTTP_METHOD, AppRouteHandlerFn>\n  )\n\n  // Get all the methods that could be automatically implemented that were not\n  // implemented by the userland module.\n  const implemented = new Set(HTTP_METHODS.filter((method) => handlers[method]))\n  const missing = AUTOMATIC_ROUTE_METHODS.filter(\n    (method) => !implemented.has(method)\n  )\n\n  // Loop over the missing methods to automatically implement them if we can.\n  for (const method of missing) {\n    // If the userland module doesn't implement the HEAD method, then\n    // we'll automatically implement it by calling the GET method (if it\n    // exists).\n    if (method === 'HEAD') {\n      if (handlers.GET) {\n        // Implement the HEAD method by calling the GET method.\n        methods.HEAD = handlers.GET\n\n        // Mark it as implemented.\n        implemented.add('HEAD')\n      }\n      continue\n    }\n\n    // If OPTIONS is not provided then implement it.\n    if (method === 'OPTIONS') {\n      // TODO: check if HEAD is implemented, if so, use it to add more headers\n\n      // Get all the methods that were implemented by the userland module.\n      const allow: HTTP_METHOD[] = ['OPTIONS', ...implemented]\n\n      // If the list of methods doesn't include HEAD, but it includes GET, then\n      // add HEAD as it's automatically implemented.\n      if (!implemented.has('HEAD') && implemented.has('GET')) {\n        allow.push('HEAD')\n      }\n\n      // Sort and join the list with commas to create the `Allow` header. See:\n      // https://httpwg.org/specs/rfc9110.html#field.allow\n      const headers = { Allow: allow.sort().join(', ') }\n\n      // Implement the OPTIONS method by returning a 204 response with the\n      // `Allow` header.\n      methods.OPTIONS = () => new Response(null, { status: 204, headers })\n\n      // Mark this method as implemented.\n      implemented.add('OPTIONS')\n\n      continue\n    }\n\n    throw new Error(\n      `Invariant: should handle all automatic implementable methods, got method: ${method}`\n    )\n  }\n\n  return methods\n}\n"], "names": ["HTTP_METHODS", "AUTOMATIC_ROUTE_METHODS", "handleMethodNotAllowedResponse", "Response", "status", "autoImplementMethods", "handlers", "methods", "reduce", "acc", "method", "implemented", "Set", "filter", "missing", "has", "GET", "HEAD", "add", "allow", "push", "headers", "Allow", "sort", "join", "OPTIONS", "Error"], "mappings": "AAEA,SAASA,YAAY,QAA0B,oBAAmB;AAElE,MAAMC,0BAA0B;IAAC;IAAQ;CAAU;AAEnD,SAASC;IACP,OAAO,IAAIC,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C;AAEA,OAAO,SAASC,qBACdC,QAA0B;IAE1B,0EAA0E;IAC1E,+DAA+D;IAC/D,MAAMC,UAAkDP,aAAaQ,MAAM,CACzE,CAACC,KAAKC,SAAY,CAAA;YAChB,GAAGD,GAAG;YACN,wEAAwE;YACxE,gCAAgC;YAChC,CAACC,OAAO,EAAEJ,QAAQ,CAACI,OAAO,IAAIR;QAChC,CAAA,GACA,CAAC;IAGH,4EAA4E;IAC5E,sCAAsC;IACtC,MAAMS,cAAc,IAAIC,IAAIZ,aAAaa,MAAM,CAAC,CAACH,SAAWJ,QAAQ,CAACI,OAAO;IAC5E,MAAMI,UAAUb,wBAAwBY,MAAM,CAC5C,CAACH,SAAW,CAACC,YAAYI,GAAG,CAACL;IAG/B,2EAA2E;IAC3E,KAAK,MAAMA,UAAUI,QAAS;QAC5B,iEAAiE;QACjE,oEAAoE;QACpE,WAAW;QACX,IAAIJ,WAAW,QAAQ;YACrB,IAAIJ,SAASU,GAAG,EAAE;gBAChB,uDAAuD;gBACvDT,QAAQU,IAAI,GAAGX,SAASU,GAAG;gBAE3B,0BAA0B;gBAC1BL,YAAYO,GAAG,CAAC;YAClB;YACA;QACF;QAEA,gDAAgD;QAChD,IAAIR,WAAW,WAAW;YACxB,wEAAwE;YAExE,oEAAoE;YACpE,MAAMS,QAAuB;gBAAC;mBAAcR;aAAY;YAExD,yEAAyE;YACzE,8CAA8C;YAC9C,IAAI,CAACA,YAAYI,GAAG,CAAC,WAAWJ,YAAYI,GAAG,CAAC,QAAQ;gBACtDI,MAAMC,IAAI,CAAC;YACb;YAEA,wEAAwE;YACxE,oDAAoD;YACpD,MAAMC,UAAU;gBAAEC,OAAOH,MAAMI,IAAI,GAAGC,IAAI,CAAC;YAAM;YAEjD,oEAAoE;YACpE,kBAAkB;YAClBjB,QAAQkB,OAAO,GAAG,IAAM,IAAItB,SAAS,MAAM;oBAAEC,QAAQ;oBAAKiB;gBAAQ;YAElE,mCAAmC;YACnCV,YAAYO,GAAG,CAAC;YAEhB;QACF;QAEA,MAAM,qBAEL,CAFK,IAAIQ,MACR,CAAC,0EAA0E,EAAEhB,QAAQ,GADjF,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOH;AACT"}
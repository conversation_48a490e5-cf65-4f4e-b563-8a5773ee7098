{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/get-source-map-from-file.ts"], "sourcesContent": ["import fs from 'fs/promises'\nimport path from 'path'\nimport url from 'url'\nimport type { RawSourceMap } from 'next/dist/compiled/source-map08'\nimport dataUriToBuffer from 'next/dist/compiled/data-uri-to-buffer'\nimport { getSourceMapUrl } from './get-source-map-url'\n\nexport async function getSourceMapFromFile(\n  filename: string\n): Promise<RawSourceMap | undefined> {\n  filename = filename.startsWith('file://')\n    ? url.fileURLToPath(filename)\n    : filename\n\n  let fileContents: string\n\n  try {\n    fileContents = await fs.readFile(filename, 'utf-8')\n  } catch (error) {\n    throw new Error(`Failed to read file contents of ${filename}.`, {\n      cause: error,\n    })\n  }\n\n  const sourceUrl = getSourceMapUrl(fileContents)\n\n  if (!sourceUrl) {\n    return undefined\n  }\n\n  if (sourceUrl.startsWith('data:')) {\n    let buffer: dataUriToBuffer.MimeBuffer\n\n    try {\n      buffer = dataUriTo<PERSON>uffer(sourceUrl)\n    } catch (error) {\n      throw new Error(`Failed to parse source map URL for ${filename}.`, {\n        cause: error,\n      })\n    }\n\n    if (buffer.type !== 'application/json') {\n      throw new Error(\n        `Unknown source map type for ${filename}: ${buffer.typeFull}.`\n      )\n    }\n\n    try {\n      return JSON.parse(buffer.toString())\n    } catch (error) {\n      throw new Error(`Failed to parse source map for ${filename}.`, {\n        cause: error,\n      })\n    }\n  }\n\n  const sourceMapFilename = path.resolve(\n    path.dirname(filename),\n    decodeURIComponent(sourceUrl)\n  )\n\n  try {\n    const sourceMapContents = await fs.readFile(sourceMapFilename, 'utf-8')\n\n    return JSON.parse(sourceMapContents.toString())\n  } catch (error) {\n    throw new Error(`Failed to parse source map ${sourceMapFilename}.`, {\n      cause: error,\n    })\n  }\n}\n"], "names": ["getSourceMapFromFile", "filename", "startsWith", "url", "fileURLToPath", "fileContents", "fs", "readFile", "error", "Error", "cause", "sourceUrl", "getSourceMapUrl", "undefined", "buffer", "dataUriToBuffer", "type", "typeFull", "JSON", "parse", "toString", "sourceMapFilename", "path", "resolve", "dirname", "decodeURIComponent", "sourceMapContents"], "mappings": ";;;;+BAOsBA;;;eAAAA;;;;mEAPP;+DACE;8DACD;0EAEY;iCACI;AAEzB,eAAeA,qBACpBC,QAAgB;IAEhBA,WAAWA,SAASC,UAAU,CAAC,aAC3BC,YAAG,CAACC,aAAa,CAACH,YAClBA;IAEJ,IAAII;IAEJ,IAAI;QACFA,eAAe,MAAMC,iBAAE,CAACC,QAAQ,CAACN,UAAU;IAC7C,EAAE,OAAOO,OAAO;QACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,qCAAkCR,WAAS,KAAI;YAC9DS,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;IAEA,MAAMG,YAAYC,IAAAA,gCAAe,EAACP;IAElC,IAAI,CAACM,WAAW;QACd,OAAOE;IACT;IAEA,IAAIF,UAAUT,UAAU,CAAC,UAAU;QACjC,IAAIY;QAEJ,IAAI;YACFA,SAASC,IAAAA,wBAAe,EAACJ;QAC3B,EAAE,OAAOH,OAAO;YACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,wCAAqCR,WAAS,KAAI;gBACjES,OAAOF;YACT,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH;QAEA,IAAIM,OAAOE,IAAI,KAAK,oBAAoB;YACtC,MAAM,qBAEL,CAFK,IAAIP,MACR,AAAC,iCAA8BR,WAAS,OAAIa,OAAOG,QAAQ,GAAC,MADxD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI;YACF,OAAOC,KAAKC,KAAK,CAACL,OAAOM,QAAQ;QACnC,EAAE,OAAOZ,OAAO;YACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,oCAAiCR,WAAS,KAAI;gBAC7DS,OAAOF;YACT,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH;IACF;IAEA,MAAMa,oBAAoBC,aAAI,CAACC,OAAO,CACpCD,aAAI,CAACE,OAAO,CAACvB,WACbwB,mBAAmBd;IAGrB,IAAI;QACF,MAAMe,oBAAoB,MAAMpB,iBAAE,CAACC,QAAQ,CAACc,mBAAmB;QAE/D,OAAOH,KAAKC,KAAK,CAACO,kBAAkBN,QAAQ;IAC9C,EAAE,OAAOZ,OAAO;QACd,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,gCAA6BY,oBAAkB,KAAI;YAClEX,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;AACF"}
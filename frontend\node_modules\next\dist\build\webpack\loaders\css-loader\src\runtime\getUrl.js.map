{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/runtime/getUrl.ts"], "sourcesContent": ["module.exports = function (url: any, options: any) {\n  if (!options) {\n    // eslint-disable-next-line no-param-reassign\n    options = {}\n  } // eslint-disable-next-line no-underscore-dangle, no-param-reassign\n\n  url = url && url.__esModule ? url.default : url\n\n  if (typeof url !== 'string') {\n    return url\n  } // If url is already wrapped in quotes, remove them\n\n  if (/^['\"].*['\"]$/.test(url)) {\n    // eslint-disable-next-line no-param-reassign\n    url = url.slice(1, -1)\n  }\n\n  if (options.hash) {\n    // eslint-disable-next-line no-param-reassign\n    url += options.hash\n  } // Should url be wrapped?\n  // See https://drafts.csswg.org/css-values-3/#urls\n\n  if (/[\"'() \\t\\n]/.test(url) || options.needQuotes) {\n    return '\"'.concat(url.replace(/\"/g, '\\\\\"').replace(/\\n/g, '\\\\n'), '\"')\n  }\n\n  return url\n}\n"], "names": ["module", "exports", "url", "options", "__esModule", "default", "test", "slice", "hash", "needQuotes", "concat", "replace"], "mappings": ";AAAAA,OAAOC,OAAO,GAAG,SAAUC,GAAQ,EAAEC,OAAY;IAC/C,IAAI,CAACA,SAAS;QACZ,6CAA6C;QAC7CA,UAAU,CAAC;IACb,EAAE,mEAAmE;IAErED,MAAMA,OAAOA,IAAIE,UAAU,GAAGF,IAAIG,OAAO,GAAGH;IAE5C,IAAI,OAAOA,QAAQ,UAAU;QAC3B,OAAOA;IACT,EAAE,mDAAmD;IAErD,IAAI,eAAeI,IAAI,CAACJ,MAAM;QAC5B,6CAA6C;QAC7CA,MAAMA,IAAIK,KAAK,CAAC,GAAG,CAAC;IACtB;IAEA,IAAIJ,QAAQK,IAAI,EAAE;QAChB,6CAA6C;QAC7CN,OAAOC,QAAQK,IAAI;IACrB,EAAE,yBAAyB;IAC3B,kDAAkD;IAElD,IAAI,cAAcF,IAAI,CAACJ,QAAQC,QAAQM,UAAU,EAAE;QACjD,OAAO,IAAIC,MAAM,CAACR,IAAIS,OAAO,CAAC,MAAM,OAAOA,OAAO,CAAC,OAAO,QAAQ;IACpE;IAEA,OAAOT;AACT"}
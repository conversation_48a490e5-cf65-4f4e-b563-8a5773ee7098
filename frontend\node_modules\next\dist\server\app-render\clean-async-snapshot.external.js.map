{"version": 3, "sources": ["../../../src/server/app-render/clean-async-snapshot.external.ts"], "sourcesContent": ["// Share the instance module in the next-shared layer\nimport { runInCleanSnapshot } from './clean-async-snapshot-instance' with { 'turbopack-transition': 'next-shared' }\n\nexport { runInCleanSnapshot }\n"], "names": ["runInCleanSnapshot"], "mappings": "AAAA,qDAAqD;;;;;+BAG5CA;;;eAAAA,8CAAkB;;;4CAFQ"}
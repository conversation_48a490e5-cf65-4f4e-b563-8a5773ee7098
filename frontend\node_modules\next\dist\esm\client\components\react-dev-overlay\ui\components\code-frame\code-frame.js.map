{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/code-frame/code-frame.tsx"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport { useMemo } from 'react'\nimport { HotlinkedText } from '../hot-linked-text'\nimport { getFrameSource } from '../../../utils/stack-frame'\nimport { useOpenInEditor } from '../../utils/use-open-in-editor'\nimport { ExternalIcon } from '../../icons/external'\nimport { FileIcon } from '../../icons/file'\nimport {\n  formatCodeFrame,\n  groupCodeFrameLines,\n  parseLineNumberFromCodeFrameLine,\n} from './parse-code-frame'\n\nexport type CodeFrameProps = { stackFrame: StackFrame; codeFrame: string }\n\nexport function CodeFrame({ stackFrame, codeFrame }: CodeFrameProps) {\n  const formattedFrame = useMemo<string>(\n    () => formatCodeFrame(codeFrame),\n    [codeFrame]\n  )\n  const decodedLines = useMemo(\n    () => groupCodeFrameLines(formattedFrame),\n    [formattedFrame]\n  )\n\n  const open = useOpenInEditor({\n    file: stackFrame.file,\n    lineNumber: stackFrame.lineNumber,\n    column: stackFrame.column,\n  })\n\n  const fileExtension = stackFrame?.file?.split('.').pop()\n\n  // TODO: make the caret absolute\n  return (\n    <div data-nextjs-codeframe>\n      <div className=\"code-frame-header\">\n        {/* TODO: This is <div> in `Terminal` component.\n        Changing now will require multiple test snapshots updates.\n        Leaving as <div> as is trivial and does not affect the UI.\n        Change when the new redbox matcher `toDisplayRedbox` is used.\n        */}\n        <p className=\"code-frame-link\">\n          <span className=\"code-frame-icon\">\n            <FileIcon lang={fileExtension} />\n          </span>\n          <span data-text>\n            {getFrameSource(stackFrame)} @{' '}\n            <HotlinkedText text={stackFrame.methodName} />\n          </span>\n          <button\n            aria-label=\"Open in editor\"\n            data-with-open-in-editor-link-source-file\n            onClick={open}\n          >\n            <span className=\"code-frame-icon\" data-icon=\"right\">\n              <ExternalIcon width={16} height={16} />\n            </span>\n          </button>\n        </p>\n      </div>\n      <pre className=\"code-frame-pre\">\n        {decodedLines.map((line, lineIndex) => {\n          const { lineNumber, isErroredLine } =\n            parseLineNumberFromCodeFrameLine(line, stackFrame)\n\n          const lineNumberProps: Record<string, string | boolean> = {}\n          if (lineNumber) {\n            lineNumberProps['data-nextjs-codeframe-line'] = lineNumber\n          }\n          if (isErroredLine) {\n            lineNumberProps['data-nextjs-codeframe-line--errored'] = true\n          }\n\n          return (\n            <div key={`line-${lineIndex}`} {...lineNumberProps}>\n              {line.map((entry, entryIndex) => (\n                <span\n                  key={`frame-${entryIndex}`}\n                  style={{\n                    color: entry.fg ? `var(--color-${entry.fg})` : undefined,\n                    ...(entry.decoration === 'bold'\n                      ? // TODO(jiwon): This used to be 800, but the symbols like `─┬─` are\n                        // having longer width than expected on Geist Mono font-weight\n                        // above 600, hence a temporary fix is to use 500 for bold.\n                        { fontWeight: 500 }\n                      : entry.decoration === 'italic'\n                        ? { fontStyle: 'italic' }\n                        : undefined),\n                  }}\n                >\n                  {entry.content}\n                </span>\n              ))}\n            </div>\n          )\n        })}\n      </pre>\n    </div>\n  )\n}\n\nexport const CODE_FRAME_STYLES = `\n  [data-nextjs-codeframe] {\n    --code-frame-padding: 12px;\n    --code-frame-line-height: var(--size-16);\n    background-color: var(--color-background-200);\n    overflow: hidden;\n    color: var(--color-gray-1000);\n    text-overflow: ellipsis;\n    border: 1px solid var(--color-gray-400);\n    border-radius: 8px;\n    font-family: var(--font-stack-monospace);\n    font-size: var(--size-12);\n    line-height: var(--code-frame-line-height);\n    margin: 8px 0;\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n  }\n\n  .code-frame-link,\n  .code-frame-pre {\n    padding: var(--code-frame-padding);\n  }\n\n  .code-frame-link svg {\n    flex-shrink: 0;\n  }\n\n  .code-frame-link [data-text] {\n    display: inline-flex;\n    text-align: left;\n    margin: auto 6px;\n  }\n\n  .code-frame-header {\n    width: 100%;\n    transition: background 100ms ease-out;\n    border-radius: 8px 8px 0 0;\n    border-bottom: 1px solid var(--color-gray-400);\n  }\n\n  [data-with-open-in-editor-link-source-file] {\n    padding: 4px;\n    margin: -4px 0 -4px auto;\n    border-radius: var(--rounded-full);\n    margin-left: auto;\n\n    &:focus-visible {\n      outline: var(--focus-ring);\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  [data-nextjs-codeframe]::selection,\n  [data-nextjs-codeframe] *::selection {\n    background-color: var(--color-ansi-selection);\n  }\n\n  [data-nextjs-codeframe] *:not(a) {\n    color: inherit;\n    background-color: transparent;\n    font-family: var(--font-stack-monospace);\n  }\n\n  [data-nextjs-codeframe-line][data-nextjs-codeframe-line--errored=\"true\"] {\n    position: relative;\n    isolation: isolate;\n\n    > span { \n      position: relative;\n      z-index: 1;\n    }\n\n    &::after {\n      content: \"\";\n      width: calc(100% + var(--code-frame-padding) * 2);\n      height: var(--code-frame-line-height);\n      left: calc(-1 * var(--code-frame-padding));\n      background: var(--color-red-200);\n      box-shadow: 2px 0 0 0 var(--color-red-900) inset;\n      position: absolute;\n    }\n  }\n\n\n  [data-nextjs-codeframe] > * {\n    margin: 0;\n  }\n\n  .code-frame-link {\n    display: flex;\n    margin: 0;\n    outline: 0;\n  }\n  .code-frame-link [data-icon='right'] {\n    margin-left: auto;\n  }\n\n  [data-nextjs-codeframe] div > pre {\n    overflow: hidden;\n    display: inline-block;\n  }\n\n  [data-nextjs-codeframe] svg {\n    color: var(--color-gray-900);\n  }\n`\n"], "names": ["useMemo", "HotlinkedText", "getFrameSource", "useOpenInEditor", "ExternalIcon", "FileIcon", "formatCodeFrame", "groupCodeFrameLines", "parseLineNumberFromCodeFrameLine", "CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "decodedLines", "open", "file", "lineNumber", "column", "fileExtension", "split", "pop", "div", "data-nextjs-codeframe", "className", "p", "span", "lang", "data-text", "text", "methodName", "button", "aria-label", "data-with-open-in-editor-link-source-file", "onClick", "data-icon", "width", "height", "pre", "map", "line", "lineIndex", "isErroredLine", "lineNumberProps", "entry", "entryIndex", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content", "CODE_FRAME_STYLES"], "mappings": ";AACA,SAASA,OAAO,QAAQ,QAAO;AAC/B,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,eAAe,QAAQ,iCAAgC;AAChE,SAASC,YAAY,QAAQ,uBAAsB;AACnD,SAASC,QAAQ,QAAQ,mBAAkB;AAC3C,SACEC,eAAe,EACfC,mBAAmB,EACnBC,gCAAgC,QAC3B,qBAAoB;AAI3B,OAAO,SAASC,UAAU,KAAyC;IAAzC,IAAA,EAAEC,UAAU,EAAEC,SAAS,EAAkB,GAAzC;QAgBFD;IAftB,MAAME,iBAAiBZ,QACrB,IAAMM,gBAAgBK,YACtB;QAACA;KAAU;IAEb,MAAME,eAAeb,QACnB,IAAMO,oBAAoBK,iBAC1B;QAACA;KAAe;IAGlB,MAAME,OAAOX,gBAAgB;QAC3BY,MAAML,WAAWK,IAAI;QACrBC,YAAYN,WAAWM,UAAU;QACjCC,QAAQP,WAAWO,MAAM;IAC3B;IAEA,MAAMC,gBAAgBR,+BAAAA,mBAAAA,WAAYK,IAAI,qBAAhBL,iBAAkBS,KAAK,CAAC,KAAKC,GAAG;IAEtD,gCAAgC;IAChC,qBACE,MAACC;QAAIC,uBAAqB;;0BACxB,KAACD;gBAAIE,WAAU;0BAMb,cAAA,MAACC;oBAAED,WAAU;;sCACX,KAACE;4BAAKF,WAAU;sCACd,cAAA,KAAClB;gCAASqB,MAAMR;;;sCAElB,MAACO;4BAAKE,WAAS;;gCACZzB,eAAeQ;gCAAY;gCAAG;8CAC/B,KAACT;oCAAc2B,MAAMlB,WAAWmB,UAAU;;;;sCAE5C,KAACC;4BACCC,cAAW;4BACXC,2CAAyC;4BACzCC,SAASnB;sCAET,cAAA,KAACW;gCAAKF,WAAU;gCAAkBW,aAAU;0CAC1C,cAAA,KAAC9B;oCAAa+B,OAAO;oCAAIC,QAAQ;;;;;;;0BAKzC,KAACC;gBAAId,WAAU;0BACZV,aAAayB,GAAG,CAAC,CAACC,MAAMC;oBACvB,MAAM,EAAExB,UAAU,EAAEyB,aAAa,EAAE,GACjCjC,iCAAiC+B,MAAM7B;oBAEzC,MAAMgC,kBAAoD,CAAC;oBAC3D,IAAI1B,YAAY;wBACd0B,eAAe,CAAC,6BAA6B,GAAG1B;oBAClD;oBACA,IAAIyB,eAAe;wBACjBC,eAAe,CAAC,sCAAsC,GAAG;oBAC3D;oBAEA,qBACE,KAACrB;wBAA+B,GAAGqB,eAAe;kCAC/CH,KAAKD,GAAG,CAAC,CAACK,OAAOC,2BAChB,KAACnB;gCAECoB,OAAO;oCACLC,OAAOH,MAAMI,EAAE,GAAG,AAAC,iBAAcJ,MAAMI,EAAE,GAAC,MAAKC;oCAC/C,GAAIL,MAAMM,UAAU,KAAK,SAErB,8DAA8D;oCAC9D,2DAA2D;oCAC3D;wCAAEC,YAAY;oCAAI,IAClBP,MAAMM,UAAU,KAAK,WACnB;wCAAEE,WAAW;oCAAS,IACtBH,SAAS;gCACjB;0CAECL,MAAMS,OAAO;+BAbT,AAAC,WAAQR;uBAHV,AAAC,UAAOJ;gBAqBtB;;;;AAIR;AAEA,OAAO,MAAMa,oBAAqB,s+EAgHjC"}
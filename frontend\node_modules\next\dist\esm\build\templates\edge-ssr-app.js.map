{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "sourcesContent": ["import '../../server/web/globals'\nimport { adapter } from '../../server/web/adapter'\nimport { getRender } from '../webpack/loaders/next-edge-ssr-loader/render'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\n\nimport { renderToHTMLOrFlight as renderToHTML } from '../../server/app-render/app-render'\nimport * as pageMod from 'VAR_USERLAND'\n\nimport type { DocumentType } from '../../shared/lib/utils'\nimport type { BuildManifest } from '../../server/get-page-files'\nimport type { RequestData } from '../../server/web/types'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { initializeCacheHandlers } from '../../server/use-cache/handlers'\n\ndeclare const incrementalCacheHandler: any\n// OPTIONAL_IMPORT:incrementalCacheHandler\n\n// Initialize the cache handlers interface.\ninitializeCacheHandlers()\n\nconst Document: DocumentType = null!\nconst appMod = null\nconst errorMod = null\nconst error500Mod = null\n\n// injected by the loader afterwards.\ndeclare const sriEnabled: boolean\ndeclare const isServerComponent: boolean\ndeclare const dev: boolean\ndeclare const serverActions: any\ndeclare const nextConfig: NextConfigComplete\n// INJECT:sriEnabled\n// INJECT:isServerComponent\n// INJECT:dev\n// INJECT:serverActions\n// INJECT:nextConfig\n\nconst maybeJSONParse = (str?: string) => (str ? JSON.parse(str) : undefined)\n\nconst buildManifest: BuildManifest = self.__BUILD_MANIFEST as any\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST)\nconst rscManifest = self.__RSC_MANIFEST?.['VAR_PAGE']\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST)\nconst subresourceIntegrityManifest = sriEnabled\n  ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST)\n  : undefined\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST)\n\nconst interceptionRouteRewrites =\n  maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? []\n\nif (rscManifest && rscServerManifest) {\n  setReferenceManifestsSingleton({\n    page: 'VAR_PAGE',\n    clientReferenceManifest: rscManifest,\n    serverActionsManifest: rscServerManifest,\n    serverModuleMap: createServerModuleMap({\n      serverActionsManifest: rscServerManifest,\n    }),\n  })\n}\n\nconst render = getRender({\n  pagesType: PAGE_TYPES.APP,\n  dev,\n  page: 'VAR_PAGE',\n  appMod,\n  pageMod,\n  errorMod,\n  error500Mod,\n  Document,\n  buildManifest,\n  renderToHTML,\n  reactLoadableManifest,\n  clientReferenceManifest: isServerComponent ? rscManifest : null,\n  serverActionsManifest: isServerComponent ? rscServerManifest : null,\n  serverActions: isServerComponent ? serverActions : undefined,\n  subresourceIntegrityManifest,\n  config: nextConfig,\n  buildId: process.env.__NEXT_BUILD_ID!,\n  nextFontManifest,\n  incrementalCacheHandler,\n  interceptionRouteRewrites,\n})\n\nexport const ComponentMod = pageMod\n\nexport default function nHandler(opts: { page: string; request: RequestData }) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    handler: render,\n  })\n}\n"], "names": ["self", "adapter", "getRender", "IncrementalCache", "renderToHTMLOrFlight", "renderToHTML", "pageMod", "PAGE_TYPES", "setReferenceManifestsSingleton", "createServerModuleMap", "initializeCacheHandlers", "Document", "appMod", "errorMod", "error500Mod", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "render", "pagesType", "APP", "dev", "isServerComponent", "serverActions", "config", "nextConfig", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "handler"], "mappings": "IA4CoBA;AA5CpB,OAAO,2BAA0B;AACjC,SAASC,OAAO,QAAQ,2BAA0B;AAClD,SAASC,SAAS,QAAQ,iDAAgD;AAC1E,SAASC,gBAAgB,QAAQ,qCAAoC;AAErE,SAASC,wBAAwBC,YAAY,QAAQ,qCAAoC;AACzF,YAAYC,aAAa,eAAc;AAMvC,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,8BAA8B,QAAQ,2CAA0C;AACzF,SAASC,qBAAqB,QAAQ,uCAAsC;AAC5E,SAASC,uBAAuB,QAAQ,kCAAiC;AAGzE,0CAA0C;AAE1C,2CAA2C;AAC3CA;AAEA,MAAMC,WAAyB;AAC/B,MAAMC,SAAS;AACf,MAAMC,WAAW;AACjB,MAAMC,cAAc;AAQpB,oBAAoB;AACpB,2BAA2B;AAC3B,aAAa;AACb,uBAAuB;AACvB,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BpB,KAAKqB,gBAAgB;AAC1D,MAAMC,wBAAwBP,eAAef,KAAKuB,yBAAyB;AAC3E,MAAMC,eAAcxB,uBAAAA,KAAKyB,cAAc,qBAAnBzB,oBAAqB,CAAC,WAAW;AACrD,MAAM0B,oBAAoBX,eAAef,KAAK2B,qBAAqB;AACnE,MAAMC,+BAA+BC,aACjCd,eAAef,KAAK8B,gCAAgC,IACpDX;AACJ,MAAMY,mBAAmBhB,eAAef,KAAKgC,oBAAoB;AAEjE,MAAMC,4BACJlB,eAAef,KAAKkC,qCAAqC,KAAK,EAAE;AAElE,IAAIV,eAAeE,mBAAmB;IACpClB,+BAA+B;QAC7B2B,MAAM;QACNC,yBAAyBZ;QACzBa,uBAAuBX;QACvBY,iBAAiB7B,sBAAsB;YACrC4B,uBAAuBX;QACzB;IACF;AACF;AAEA,MAAMa,SAASrC,UAAU;IACvBsC,WAAWjC,WAAWkC,GAAG;IACzBC;IACAP,MAAM;IACNvB;IACAN;IACAO;IACAC;IACAH;IACAS;IACAf;IACAiB;IACAc,yBAAyBO,oBAAoBnB,cAAc;IAC3Da,uBAAuBM,oBAAoBjB,oBAAoB;IAC/DkB,eAAeD,oBAAoBC,gBAAgBzB;IACnDS;IACAiB,QAAQC;IACRC,SAASC,QAAQC,GAAG,CAACC,eAAe;IACpCnB;IACAoB;IACAlB;AACF;AAEA,OAAO,MAAMmB,eAAe9C,QAAO;AAEnC,eAAe,SAAS+C,SAASC,IAA4C;IAC3E,OAAOrD,QAAQ;QACb,GAAGqD,IAAI;QACPnD;QACAoD,SAAShB;IACX;AACF"}
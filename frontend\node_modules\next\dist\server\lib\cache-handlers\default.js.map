{"version": 3, "sources": ["../../../../src/server/lib/cache-handlers/default.ts"], "sourcesContent": ["/**\n * This is the default \"use cache\" handler it defaults to an in-memory store.\n * In-memory caches are fragile and should not use stale-while-revalidate\n * semantics on the caches because it's not worth warming up an entry that's\n * likely going to get evicted before we get to use it anyway. However, we also\n * don't want to reuse a stale entry for too long so stale entries should be\n * considered expired/missing in such cache handlers.\n */\n\nimport { LRUCache } from '../lru-cache'\nimport type { CacheEntry, CacheHandlerV2 } from './types'\nimport {\n  isStale,\n  tagsManifest,\n} from '../incremental-cache/tags-manifest.external'\n\ntype PrivateCacheEntry = {\n  entry: CacheEntry\n\n  // For the default cache we store errored cache\n  // entries and allow them to be used up to 3 times\n  // after that we want to dispose it and try for fresh\n\n  // If an entry is errored we return no entry\n  // three times so that we retry hitting origin (MISS)\n  // and then if it still fails to set after the third we\n  // return the errored content and use expiration of\n  // Math.min(30, entry.expiration)\n  isErrored: boolean\n  errorRetryCount: number\n\n  // compute size on set since we need to read size\n  // of the ReadableStream for LRU evicting\n  size: number\n}\n\n// LRU cache default to max 50 MB but in future track\nconst memoryCache = new LRUCache<PrivateCacheEntry>(\n  50 * 1024 * 1024,\n  (entry) => entry.size\n)\nconst pendingSets = new Map<string, Promise<void>>()\n\nconst debug = process.env.NEXT_PRIVATE_DEBUG_CACHE\n  ? console.debug.bind(console, 'DefaultCacheHandler:')\n  : undefined\n\nconst DefaultCacheHandler: CacheHandlerV2 = {\n  async get(cacheKey) {\n    const pendingPromise = pendingSets.get(cacheKey)\n\n    if (pendingPromise) {\n      debug?.('get', cacheKey, 'pending')\n      await pendingPromise\n    }\n\n    const privateEntry = memoryCache.get(cacheKey)\n\n    if (!privateEntry) {\n      debug?.('get', cacheKey, 'not found')\n      return undefined\n    }\n\n    const entry = privateEntry.entry\n    if (\n      performance.timeOrigin + performance.now() >\n      entry.timestamp + entry.revalidate * 1000\n    ) {\n      // In-memory caches should expire after revalidate time because it is\n      // unlikely that a new entry will be able to be used before it is dropped\n      // from the cache.\n      debug?.('get', cacheKey, 'expired')\n\n      return undefined\n    }\n\n    if (isStale(entry.tags, entry.timestamp)) {\n      debug?.('get', cacheKey, 'had stale tag')\n\n      return undefined\n    }\n    const [returnStream, newSaved] = entry.value.tee()\n    entry.value = newSaved\n\n    debug?.('get', cacheKey, 'found', {\n      tags: entry.tags,\n      timestamp: entry.timestamp,\n      revalidate: entry.revalidate,\n      expire: entry.expire,\n    })\n\n    return {\n      ...entry,\n      value: returnStream,\n    }\n  },\n\n  async set(cacheKey, pendingEntry) {\n    debug?.('set', cacheKey, 'start')\n\n    let resolvePending: () => void = () => {}\n    const pendingPromise = new Promise<void>((resolve) => {\n      resolvePending = resolve\n    })\n    pendingSets.set(cacheKey, pendingPromise)\n\n    const entry = await pendingEntry\n\n    let size = 0\n\n    try {\n      const [value, clonedValue] = entry.value.tee()\n      entry.value = value\n      const reader = clonedValue.getReader()\n\n      for (let chunk; !(chunk = await reader.read()).done; ) {\n        size += Buffer.from(chunk.value).byteLength\n      }\n\n      memoryCache.set(cacheKey, {\n        entry,\n        isErrored: false,\n        errorRetryCount: 0,\n        size,\n      })\n\n      debug?.('set', cacheKey, 'done')\n    } catch (err) {\n      // TODO: store partial buffer with error after we retry 3 times\n      debug?.('set', cacheKey, 'failed', err)\n    } finally {\n      resolvePending()\n      pendingSets.delete(cacheKey)\n    }\n  },\n\n  async refreshTags() {\n    // Nothing to do for an in-memory cache handler.\n  },\n\n  async getExpiration(...tags) {\n    const expiration = Math.max(\n      ...tags.map((tag) => tagsManifest.get(tag) ?? 0)\n    )\n\n    debug?.('getExpiration', { tags, expiration })\n\n    return expiration\n  },\n\n  async expireTags(...tags) {\n    const timestamp = Math.round(performance.timeOrigin + performance.now())\n    debug?.('expireTags', { tags, timestamp })\n\n    for (const tag of tags) {\n      // TODO: update file-system-cache?\n      tagsManifest.set(tag, timestamp)\n    }\n  },\n}\n\nexport default DefaultCacheHandler\n"], "names": ["memoryCache", "L<PERSON><PERSON><PERSON>", "entry", "size", "pendingSets", "Map", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "console", "bind", "undefined", "DefaultCache<PERSON>andler", "get", "cache<PERSON>ey", "pendingPromise", "privateEntry", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "timestamp", "revalidate", "isStale", "tags", "returnStream", "newSaved", "value", "tee", "expire", "set", "pendingEntry", "resolvePending", "Promise", "resolve", "clonedV<PERSON>ue", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunk", "read", "done", "<PERSON><PERSON><PERSON>", "from", "byteLength", "isErrored", "errorRetryCount", "err", "delete", "refreshTags", "getExpiration", "expiration", "Math", "max", "map", "tag", "tagsManifest", "expireTags", "round"], "mappings": "AAAA;;;;;;;CAOC;;;;+BA0JD;;;eAAA;;;0BAxJyB;sCAKlB;AAsBP,qDAAqD;AACrD,MAAMA,cAAc,IAAIC,kBAAQ,CAC9B,KAAK,OAAO,MACZ,CAACC,QAAUA,MAAMC,IAAI;AAEvB,MAAMC,cAAc,IAAIC;AAExB,MAAMC,QAAQC,QAAQC,GAAG,CAACC,wBAAwB,GAC9CC,QAAQJ,KAAK,CAACK,IAAI,CAACD,SAAS,0BAC5BE;AAEJ,MAAMC,sBAAsC;IAC1C,MAAMC,KAAIC,QAAQ;QAChB,MAAMC,iBAAiBZ,YAAYU,GAAG,CAACC;QAEvC,IAAIC,gBAAgB;YAClBV,yBAAAA,MAAQ,OAAOS,UAAU;YACzB,MAAMC;QACR;QAEA,MAAMC,eAAejB,YAAYc,GAAG,CAACC;QAErC,IAAI,CAACE,cAAc;YACjBX,yBAAAA,MAAQ,OAAOS,UAAU;YACzB,OAAOH;QACT;QAEA,MAAMV,QAAQe,aAAaf,KAAK;QAChC,IACEgB,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KACxClB,MAAMmB,SAAS,GAAGnB,MAAMoB,UAAU,GAAG,MACrC;YACA,qEAAqE;YACrE,yEAAyE;YACzE,kBAAkB;YAClBhB,yBAAAA,MAAQ,OAAOS,UAAU;YAEzB,OAAOH;QACT;QAEA,IAAIW,IAAAA,6BAAO,EAACrB,MAAMsB,IAAI,EAAEtB,MAAMmB,SAAS,GAAG;YACxCf,yBAAAA,MAAQ,OAAOS,UAAU;YAEzB,OAAOH;QACT;QACA,MAAM,CAACa,cAAcC,SAAS,GAAGxB,MAAMyB,KAAK,CAACC,GAAG;QAChD1B,MAAMyB,KAAK,GAAGD;QAEdpB,yBAAAA,MAAQ,OAAOS,UAAU,SAAS;YAChCS,MAAMtB,MAAMsB,IAAI;YAChBH,WAAWnB,MAAMmB,SAAS;YAC1BC,YAAYpB,MAAMoB,UAAU;YAC5BO,QAAQ3B,MAAM2B,MAAM;QACtB;QAEA,OAAO;YACL,GAAG3B,KAAK;YACRyB,OAAOF;QACT;IACF;IAEA,MAAMK,KAAIf,QAAQ,EAAEgB,YAAY;QAC9BzB,yBAAAA,MAAQ,OAAOS,UAAU;QAEzB,IAAIiB,iBAA6B,KAAO;QACxC,MAAMhB,iBAAiB,IAAIiB,QAAc,CAACC;YACxCF,iBAAiBE;QACnB;QACA9B,YAAY0B,GAAG,CAACf,UAAUC;QAE1B,MAAMd,QAAQ,MAAM6B;QAEpB,IAAI5B,OAAO;QAEX,IAAI;YACF,MAAM,CAACwB,OAAOQ,YAAY,GAAGjC,MAAMyB,KAAK,CAACC,GAAG;YAC5C1B,MAAMyB,KAAK,GAAGA;YACd,MAAMS,SAASD,YAAYE,SAAS;YAEpC,IAAK,IAAIC,OAAO,CAAC,AAACA,CAAAA,QAAQ,MAAMF,OAAOG,IAAI,EAAC,EAAGC,IAAI,EAAI;gBACrDrC,QAAQsC,OAAOC,IAAI,CAACJ,MAAMX,KAAK,EAAEgB,UAAU;YAC7C;YAEA3C,YAAY8B,GAAG,CAACf,UAAU;gBACxBb;gBACA0C,WAAW;gBACXC,iBAAiB;gBACjB1C;YACF;YAEAG,yBAAAA,MAAQ,OAAOS,UAAU;QAC3B,EAAE,OAAO+B,KAAK;YACZ,+DAA+D;YAC/DxC,yBAAAA,MAAQ,OAAOS,UAAU,UAAU+B;QACrC,SAAU;YACRd;YACA5B,YAAY2C,MAAM,CAAChC;QACrB;IACF;IAEA,MAAMiC;IACJ,gDAAgD;IAClD;IAEA,MAAMC,eAAc,GAAGzB,IAAI;QACzB,MAAM0B,aAAaC,KAAKC,GAAG,IACtB5B,KAAK6B,GAAG,CAAC,CAACC,MAAQC,kCAAY,CAACzC,GAAG,CAACwC,QAAQ;QAGhDhD,yBAAAA,MAAQ,iBAAiB;YAAEkB;YAAM0B;QAAW;QAE5C,OAAOA;IACT;IAEA,MAAMM,YAAW,GAAGhC,IAAI;QACtB,MAAMH,YAAY8B,KAAKM,KAAK,CAACvC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QACrEd,yBAAAA,MAAQ,cAAc;YAAEkB;YAAMH;QAAU;QAExC,KAAK,MAAMiC,OAAO9B,KAAM;YACtB,kCAAkC;YAClC+B,kCAAY,CAACzB,GAAG,CAACwB,KAAKjC;QACxB;IACF;AACF;MAEA,WAAeR"}
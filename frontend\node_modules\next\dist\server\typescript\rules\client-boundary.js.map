{"version": 3, "sources": ["../../../../src/server/typescript/rules/client-boundary.ts"], "sourcesContent": ["// This module provides intellisense for all components that has the `\"use client\"` directive.\n\nimport { NEXT_TS_ERRORS } from '../constant'\nimport { getTs, getTypeChecker } from '../utils'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst clientBoundary = {\n  getSemanticDiagnosticsForExportVariableStatement(\n    source: tsModule.SourceFile,\n    node: tsModule.VariableStatement\n  ) {\n    const ts = getTs()\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    if (ts.isVariableDeclarationList(node.declarationList)) {\n      for (const declaration of node.declarationList.declarations) {\n        const initializer = declaration.initializer\n        if (initializer && ts.isArrowFunction(initializer)) {\n          diagnostics.push(\n            ...clientBoundary.getSemanticDiagnosticsForFunctionExport(\n              source,\n              initializer\n            )\n          )\n        }\n      }\n    }\n\n    return diagnostics\n  },\n\n  getSemanticDiagnosticsForFunctionExport(\n    source: tsModule.SourceFile,\n    node: tsModule.FunctionDeclaration | tsModule.ArrowFunction\n  ) {\n    const ts = getTs()\n    const typeChecker = getTypeChecker()\n    if (!typeChecker) return []\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    const isErrorFile = /[\\\\/]error\\.tsx?$/.test(source.fileName)\n    const isGlobalErrorFile = /[\\\\/]global-error\\.tsx?$/.test(source.fileName)\n\n    const props = node.parameters?.[0]?.name\n    if (props && ts.isObjectBindingPattern(props)) {\n      for (const prop of (props as tsModule.ObjectBindingPattern).elements) {\n        const type = typeChecker.getTypeAtLocation(prop)\n        const typeDeclarationNode = type.symbol?.getDeclarations()?.[0]\n        const propName = (prop.propertyName || prop.name).getText()\n\n        if (typeDeclarationNode) {\n          if (ts.isFunctionTypeNode(typeDeclarationNode)) {\n            // By convention, props named \"action\" can accept functions since we\n            // assume these are Server Actions. Structurally, there's no\n            // difference between a Server Action and a normal function until\n            // TypeScript exposes directives in the type of a function. This\n            // will miss accidentally passing normal functions but a false\n            // negative is better than a false positive given how frequent the\n            // false-positive would be.\n            const maybeServerAction =\n              propName === 'action' || /.+Action$/.test(propName)\n\n            // There's a special case for the error file that the `reset` prop\n            // is allowed to be a function:\n            // https://github.com/vercel/next.js/issues/46573\n            const isErrorReset =\n              (isErrorFile || isGlobalErrorFile) && propName === 'reset'\n\n            if (!maybeServerAction && !isErrorReset) {\n              diagnostics.push({\n                file: source,\n                category: ts.DiagnosticCategory.Warning,\n                code: NEXT_TS_ERRORS.INVALID_CLIENT_ENTRY_PROP,\n                messageText:\n                  `Props must be serializable for components in the \"use client\" entry file. ` +\n                  `\"${propName}\" is a function that's not a Server Action. ` +\n                  `Rename \"${propName}\" either to \"action\" or have its name end with \"Action\" e.g. \"${propName}Action\" to indicate it is a Server Action.`,\n                start: prop.getStart(),\n                length: prop.getWidth(),\n              })\n            }\n          } else if (\n            // Show warning for not serializable props.\n            ts.isConstructorTypeNode(typeDeclarationNode) ||\n            ts.isClassDeclaration(typeDeclarationNode)\n          ) {\n            diagnostics.push({\n              file: source,\n              category: ts.DiagnosticCategory.Warning,\n              code: NEXT_TS_ERRORS.INVALID_CLIENT_ENTRY_PROP,\n              messageText: `Props must be serializable for components in the \"use client\" entry file, \"${propName}\" is invalid.`,\n              start: prop.getStart(),\n              length: prop.getWidth(),\n            })\n          }\n        }\n      }\n    }\n\n    return diagnostics\n  },\n}\n\nexport default clientBoundary\n"], "names": ["clientBoundary", "getSemanticDiagnosticsForExportVariableStatement", "source", "node", "ts", "getTs", "diagnostics", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "initializer", "isArrowFunction", "push", "getSemanticDiagnosticsForFunctionExport", "typeC<PERSON>cker", "getType<PERSON><PERSON>cker", "isErrorFile", "test", "fileName", "isGlobalErrorFile", "props", "parameters", "name", "isObjectBindingPattern", "prop", "elements", "type", "getTypeAtLocation", "typeDeclarationNode", "symbol", "getDeclarations", "propName", "propertyName", "getText", "isFunctionTypeNode", "maybeServerAction", "isErrorReset", "file", "category", "DiagnosticCategory", "Warning", "code", "NEXT_TS_ERRORS", "INVALID_CLIENT_ENTRY_PROP", "messageText", "start", "getStart", "length", "getWidth", "isConstructorTypeNode", "isClassDeclaration"], "mappings": "AAAA,8FAA8F;;;;;+BAyG9F;;;eAAA;;;0BAvG+B;uBACO;AAGtC,MAAMA,iBAAiB;IACrBC,kDACEC,MAA2B,EAC3BC,IAAgC;QAEhC,MAAMC,KAAKC,IAAAA,YAAK;QAEhB,MAAMC,cAAqC,EAAE;QAE7C,IAAIF,GAAGG,yBAAyB,CAACJ,KAAKK,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeN,KAAKK,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,cAAcF,YAAYE,WAAW;gBAC3C,IAAIA,eAAeP,GAAGQ,eAAe,CAACD,cAAc;oBAClDL,YAAYO,IAAI,IACXb,eAAec,uCAAuC,CACvDZ,QACAS;gBAGN;YACF;QACF;QAEA,OAAOL;IACT;IAEAQ,yCACEZ,MAA2B,EAC3BC,IAA2D;YAW7CA,mBAAAA;QATd,MAAMC,KAAKC,IAAAA,YAAK;QAChB,MAAMU,cAAcC,IAAAA,qBAAc;QAClC,IAAI,CAACD,aAAa,OAAO,EAAE;QAE3B,MAAMT,cAAqC,EAAE;QAE7C,MAAMW,cAAc,oBAAoBC,IAAI,CAAChB,OAAOiB,QAAQ;QAC5D,MAAMC,oBAAoB,2BAA2BF,IAAI,CAAChB,OAAOiB,QAAQ;QAEzE,MAAME,SAAQlB,mBAAAA,KAAKmB,UAAU,sBAAfnB,oBAAAA,gBAAiB,CAAC,EAAE,qBAApBA,kBAAsBoB,IAAI;QACxC,IAAIF,SAASjB,GAAGoB,sBAAsB,CAACH,QAAQ;YAC7C,KAAK,MAAMI,QAAQ,AAACJ,MAAwCK,QAAQ,CAAE;oBAExCC,8BAAAA;gBAD5B,MAAMA,OAAOZ,YAAYa,iBAAiB,CAACH;gBAC3C,MAAMI,uBAAsBF,eAAAA,KAAKG,MAAM,sBAAXH,+BAAAA,aAAaI,eAAe,uBAA5BJ,4BAAgC,CAAC,EAAE;gBAC/D,MAAMK,WAAW,AAACP,CAAAA,KAAKQ,YAAY,IAAIR,KAAKF,IAAI,AAAD,EAAGW,OAAO;gBAEzD,IAAIL,qBAAqB;oBACvB,IAAIzB,GAAG+B,kBAAkB,CAACN,sBAAsB;wBAC9C,oEAAoE;wBACpE,4DAA4D;wBAC5D,iEAAiE;wBACjE,gEAAgE;wBAChE,8DAA8D;wBAC9D,kEAAkE;wBAClE,2BAA2B;wBAC3B,MAAMO,oBACJJ,aAAa,YAAY,YAAYd,IAAI,CAACc;wBAE5C,kEAAkE;wBAClE,+BAA+B;wBAC/B,iDAAiD;wBACjD,MAAMK,eACJ,AAACpB,CAAAA,eAAeG,iBAAgB,KAAMY,aAAa;wBAErD,IAAI,CAACI,qBAAqB,CAACC,cAAc;4BACvC/B,YAAYO,IAAI,CAAC;gCACfyB,MAAMpC;gCACNqC,UAAUnC,GAAGoC,kBAAkB,CAACC,OAAO;gCACvCC,MAAMC,wBAAc,CAACC,yBAAyB;gCAC9CC,aACE,CAAC,0EAA0E,CAAC,GAC5E,CAAC,CAAC,EAAEb,SAAS,4CAA4C,CAAC,GAC1D,CAAC,QAAQ,EAAEA,SAAS,8DAA8D,EAAEA,SAAS,0CAA0C,CAAC;gCAC1Ic,OAAOrB,KAAKsB,QAAQ;gCACpBC,QAAQvB,KAAKwB,QAAQ;4BACvB;wBACF;oBACF,OAAO,IACL,2CAA2C;oBAC3C7C,GAAG8C,qBAAqB,CAACrB,wBACzBzB,GAAG+C,kBAAkB,CAACtB,sBACtB;wBACAvB,YAAYO,IAAI,CAAC;4BACfyB,MAAMpC;4BACNqC,UAAUnC,GAAGoC,kBAAkB,CAACC,OAAO;4BACvCC,MAAMC,wBAAc,CAACC,yBAAyB;4BAC9CC,aAAa,CAAC,2EAA2E,EAAEb,SAAS,aAAa,CAAC;4BAClHc,OAAOrB,KAAKsB,QAAQ;4BACpBC,QAAQvB,KAAKwB,QAAQ;wBACvB;oBACF;gBACF;YACF;QACF;QAEA,OAAO3C;IACT;AACF;MAEA,WAAeN"}
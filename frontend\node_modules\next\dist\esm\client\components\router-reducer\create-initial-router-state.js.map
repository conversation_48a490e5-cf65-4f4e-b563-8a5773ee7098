{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightDataPath } from '../../../server/app-render/types'\n\nimport { createHrefFromUrl } from './create-href-from-url'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { extractPathFromFlightRouterState } from './compute-changed-path'\nimport { createSeededPrefetchCacheEntry } from './prefetch-cache-utils'\nimport { PrefetchKind, type PrefetchCacheEntry } from './router-reducer-types'\nimport { addRefreshMarkerToActiveParallelSegments } from './refetch-inactive-parallel-segments'\nimport { getFlightDataPartsFromPath } from '../../flight-data-helpers'\n\nexport interface InitialRouterStateParameters {\n  navigatedAt: number\n  initialCanonicalUrlParts: string[]\n  initialParallelRoutes: CacheNode['parallelRoutes']\n  initialFlightData: FlightDataPath[]\n  location: Location | null\n  couldBeIntercepted: boolean\n  postponed: boolean\n  prerendered: boolean\n}\n\nexport function createInitialRouterState({\n  navigatedAt,\n  initialFlightData,\n  initialCanonicalUrlParts,\n  initialParallelRoutes,\n  location,\n  couldBeIntercepted,\n  postponed,\n  prerendered,\n}: InitialRouterStateParameters) {\n  // When initialized on the server, the canonical URL is provided as an array of parts.\n  // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n  // as a URL that should be crawled.\n  const initialCanonicalUrl = initialCanonicalUrlParts.join('/')\n  const normalizedFlightData = getFlightDataPartsFromPath(initialFlightData[0])\n  const {\n    tree: initialTree,\n    seedData: initialSeedData,\n    head: initialHead,\n  } = normalizedFlightData\n  // For the SSR render, seed data should always be available (we only send back a `null` response\n  // in the case of a `loading` segment, pre-PPR.)\n  const rsc = initialSeedData?.[1]\n  const loading = initialSeedData?.[3] ?? null\n\n  const cache: CacheNode = {\n    lazyData: null,\n    rsc,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n    parallelRoutes: initialParallelRoutes,\n    loading,\n    navigatedAt,\n  }\n\n  const canonicalUrl =\n    // location.href is read as the initial value for canonicalUrl in the browser\n    // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location\n      ? // window.location does not have the same type as URL but has all the fields createHrefFromUrl needs.\n        createHrefFromUrl(location)\n      : initialCanonicalUrl\n\n  addRefreshMarkerToActiveParallelSegments(initialTree, canonicalUrl)\n\n  const prefetchCache = new Map<string, PrefetchCacheEntry>()\n\n  // When the cache hasn't been seeded yet we fill the cache with the head.\n  if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      cache,\n      undefined,\n      initialTree,\n      initialSeedData,\n      initialHead,\n      undefined\n    )\n  }\n\n  const initialState = {\n    tree: initialTree,\n    cache,\n    prefetchCache,\n    pushRef: {\n      pendingPush: false,\n      mpaNavigation: false,\n      // First render needs to preserve the previous window.history.state\n      // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n      preserveCustomHistoryState: true,\n    },\n    focusAndScrollRef: {\n      apply: false,\n      onlyHashChange: false,\n      hashFragment: null,\n      segmentPaths: [],\n    },\n    canonicalUrl,\n    nextUrl:\n      // the || operator is intentional, the pathname can be an empty string\n      (extractPathFromFlightRouterState(initialTree) || location?.pathname) ??\n      null,\n  }\n\n  if (process.env.NODE_ENV !== 'development' && location) {\n    // Seed the prefetch cache with this page's data.\n    // This is to prevent needlessly re-prefetching a page that is already reusable,\n    // and will avoid triggering a loading state/data fetch stall when navigating back to the page.\n    // We don't currently do this in development because links aren't prefetched in development\n    // so having a mismatch between prefetch/no prefetch provides inconsistent behavior based on which page\n    // was loaded first.\n    const url = new URL(\n      `${location.pathname}${location.search}`,\n      location.origin\n    )\n\n    createSeededPrefetchCacheEntry({\n      url,\n      data: {\n        flightData: [normalizedFlightData],\n        canonicalUrl: undefined,\n        couldBeIntercepted: !!couldBeIntercepted,\n        prerendered,\n        postponed,\n        // TODO: The initial RSC payload includes both static and dynamic data\n        // in the same response, even if PPR is enabled. So if there's any\n        // dynamic data at all, we can't set a stale time. In the future we may\n        // add a way to split a single Flight stream into static and dynamic\n        // parts. But in the meantime we should at least make this work for\n        // fully static pages.\n        staleTime: -1,\n      },\n      tree: initialState.tree,\n      prefetchCache: initialState.prefetchCache,\n      nextUrl: initialState.nextUrl,\n      kind: prerendered ? PrefetchKind.FULL : PrefetchKind.AUTO,\n    })\n  }\n\n  return initialState\n}\n"], "names": ["createHrefFromUrl", "fillLazyItemsTillLeafWithHead", "extractPathFromFlightRouterState", "createSeededPrefetchCacheEntry", "PrefetchKind", "addRefreshMarkerToActiveParallelSegments", "getFlightDataPartsFromPath", "createInitialRouterState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "location", "couldBeIntercepted", "postponed", "prerendered", "initialCanonicalUrl", "join", "normalizedFlightData", "tree", "initialTree", "seedData", "initialSeedData", "head", "initialHead", "rsc", "loading", "cache", "lazyData", "prefetchRsc", "prefetchHead", "parallelRoutes", "canonicalUrl", "prefetchCache", "Map", "size", "undefined", "initialState", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "nextUrl", "pathname", "process", "env", "NODE_ENV", "url", "URL", "search", "origin", "data", "flightData", "staleTime", "kind", "FULL", "AUTO"], "mappings": "AAGA,SAASA,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,gCAAgC,QAAQ,yBAAwB;AACzE,SAASC,8BAA8B,QAAQ,yBAAwB;AACvE,SAASC,YAAY,QAAiC,yBAAwB;AAC9E,SAASC,wCAAwC,QAAQ,uCAAsC;AAC/F,SAASC,0BAA0B,QAAQ,4BAA2B;AAatE,OAAO,SAASC,yBAAyB,KASV;IATU,IAAA,EACvCC,WAAW,EACXC,iBAAiB,EACjBC,wBAAwB,EACxBC,qBAAqB,EACrBC,QAAQ,EACRC,kBAAkB,EAClBC,SAAS,EACTC,WAAW,EACkB,GATU;IAUvC,sFAAsF;IACtF,kGAAkG;IAClG,mCAAmC;IACnC,MAAMC,sBAAsBN,yBAAyBO,IAAI,CAAC;IAC1D,MAAMC,uBAAuBZ,2BAA2BG,iBAAiB,CAAC,EAAE;IAC5E,MAAM,EACJU,MAAMC,WAAW,EACjBC,UAAUC,eAAe,EACzBC,MAAMC,WAAW,EAClB,GAAGN;IACJ,gGAAgG;IAChG,gDAAgD;IAChD,MAAMO,MAAMH,mCAAAA,eAAiB,CAAC,EAAE;QAChBA;IAAhB,MAAMI,UAAUJ,CAAAA,oBAAAA,mCAAAA,eAAiB,CAAC,EAAE,YAApBA,oBAAwB;IAExC,MAAMK,QAAmB;QACvBC,UAAU;QACVH;QACAI,aAAa;QACbN,MAAM;QACNO,cAAc;QACd,oJAAoJ;QACpJC,gBAAgBpB;QAChBe;QACAlB;IACF;IAEA,MAAMwB,eACJ,6EAA6E;IAC7E,kJAAkJ;IAClJpB,WAEIZ,kBAAkBY,YAClBI;IAENX,yCAAyCe,aAAaY;IAEtD,MAAMC,gBAAgB,IAAIC;IAE1B,yEAAyE;IACzE,IAAIvB,0BAA0B,QAAQA,sBAAsBwB,IAAI,KAAK,GAAG;QACtElC,8BACEO,aACAmB,OACAS,WACAhB,aACAE,iBACAE,aACAY;IAEJ;QAqBI,sEAAsE;IACrElC;IApBL,MAAMmC,eAAe;QACnBlB,MAAMC;QACNO;QACAM;QACAK,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,mEAAmE;YACnE,gFAAgF;YAChFC,4BAA4B;QAC9B;QACAC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAd;QACAe,SAEE,CAAC7C,OAAAA,iCAAiCkB,iBAAgBR,4BAAAA,SAAUoC,QAAQ,aAAnE9C,OACD;IACJ;IAEA,IAAI+C,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBvC,UAAU;QACtD,iDAAiD;QACjD,gFAAgF;QAChF,+FAA+F;QAC/F,2FAA2F;QAC3F,uGAAuG;QACvG,oBAAoB;QACpB,MAAMwC,MAAM,IAAIC,IACd,AAAC,KAAEzC,SAASoC,QAAQ,GAAGpC,SAAS0C,MAAM,EACtC1C,SAAS2C,MAAM;QAGjBpD,+BAA+B;YAC7BiD;YACAI,MAAM;gBACJC,YAAY;oBAACvC;iBAAqB;gBAClCc,cAAcI;gBACdvB,oBAAoB,CAAC,CAACA;gBACtBE;gBACAD;gBACA,sEAAsE;gBACtE,kEAAkE;gBAClE,uEAAuE;gBACvE,oEAAoE;gBACpE,mEAAmE;gBACnE,sBAAsB;gBACtB4C,WAAW,CAAC;YACd;YACAvC,MAAMkB,aAAalB,IAAI;YACvBc,eAAeI,aAAaJ,aAAa;YACzCc,SAASV,aAAaU,OAAO;YAC7BY,MAAM5C,cAAcX,aAAawD,IAAI,GAAGxD,aAAayD,IAAI;QAC3D;IACF;IAEA,OAAOxB;AACT"}
{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.tsx"], "sourcesContent": ["import { Dialog } from '../../dialog/dialog'\n\ntype ErrorOverlayDialogProps = {\n  children?: React.ReactNode\n  onClose?: () => void\n  dialogResizerRef?: React.RefObject<HTMLDivElement | null>\n  footer?: React.ReactNode\n} & React.HTMLAttributes<HTMLDivElement>\n\nexport function ErrorOverlayDialog({\n  children,\n  onClose,\n  footer,\n  ...props\n}: ErrorOverlayDialogProps) {\n  return (\n    <div className=\"error-overlay-dialog-container\">\n      <Dialog\n        type=\"error\"\n        aria-labelledby=\"nextjs__container_errors_label\"\n        aria-describedby=\"nextjs__container_errors_desc\"\n        className=\"error-overlay-dialog-scroll\"\n        onClose={onClose}\n        {...props}\n      >\n        {children}\n      </Dialog>\n      {footer}\n    </div>\n  )\n}\n\nexport const DIALOG_STYLES = `\n  .error-overlay-dialog-container {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    flex-direction: column;\n    background: var(--color-background-100);\n    background-clip: padding-box;\n    border: var(--next-dialog-border-width) solid var(--color-gray-400);\n    border-radius: 0 0 var(--next-dialog-radius) var(--next-dialog-radius);\n    box-shadow: var(--shadow-menu);\n    position: relative;\n    overflow: hidden;\n  }\n\n  .error-overlay-dialog-scroll {\n    overflow-y: auto;\n    height: 100%;\n  }\n`\n"], "names": ["Dialog", "ErrorOverlayDialog", "children", "onClose", "footer", "props", "div", "className", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "DIALOG_STYLES"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,sBAAqB;AAS5C,OAAO,SAASC,mBAAmB,KAKT;IALS,IAAA,EACjCC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACN,GAAGC,OACqB,GALS;IAMjC,qBACE,MAACC;QAAIC,WAAU;;0BACb,KAACP;gBACCQ,MAAK;gBACLC,mBAAgB;gBAChBC,oBAAiB;gBACjBH,WAAU;gBACVJ,SAASA;gBACR,GAAGE,KAAK;0BAERH;;YAEFE;;;AAGP;AAEA,OAAO,MAAMO,gBAAiB,0hBAkB7B"}
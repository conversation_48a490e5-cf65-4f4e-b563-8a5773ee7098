{"version": 3, "sources": ["../../src/telemetry/detached-flush.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport type { TelemetryEvent } from './storage'\nimport { Telemetry } from './storage'\nimport loadConfig from '../server/config'\nimport { getProjectDir } from '../lib/get-project-dir'\nimport { PHASE_DEVELOPMENT_SERVER } from '../shared/lib/constants'\n\n// this process should be started with following arg order\n// 1. mode e.g. dev, export, start\n// 2. project dir\n;(async () => {\n  const args = [...process.argv]\n  let dir = args.pop()\n  const mode = args.pop()\n\n  if (!dir || mode !== 'dev') {\n    throw new Error(\n      `Invalid flags should be run as node detached-flush dev ./path-to/project`\n    )\n  }\n  dir = getProjectDir(dir)\n\n  const config = await loadConfig(PHASE_DEVELOPMENT_SERVER, dir)\n  const distDir = path.join(dir, config.distDir || '.next')\n  const eventsPath = path.join(distDir, '_events.json')\n\n  let events: TelemetryEvent[]\n  try {\n    events = JSON.parse(fs.readFileSync(eventsPath, 'utf8'))\n  } catch (err: any) {\n    if (err.code === 'ENOENT') {\n      // no events to process we can exit now\n      process.exit(0)\n    }\n    throw err\n  }\n\n  const telemetry = new Telemetry({ distDir })\n  await telemetry.record(events)\n  await telemetry.flush()\n\n  // finished flushing events clean-up/exit\n  fs.unlinkSync(eventsPath)\n  process.exit(0)\n})()\n"], "names": ["args", "process", "argv", "dir", "pop", "mode", "Error", "getProjectDir", "config", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "distDir", "path", "join", "eventsPath", "events", "JSON", "parse", "fs", "readFileSync", "err", "code", "exit", "telemetry", "Telemetry", "record", "flush", "unlinkSync"], "mappings": ";;;;2DAAe;6DACE;yBAES;+DACH;+BACO;2BACW;;;;;;AAKvC,CAAA;IACA,MAAMA,OAAO;WAAIC,QAAQC,IAAI;KAAC;IAC9B,IAAIC,MAAMH,KAAKI,GAAG;IAClB,MAAMC,OAAOL,KAAKI,GAAG;IAErB,IAAI,CAACD,OAAOE,SAAS,OAAO;QAC1B,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,wEAAwE,CAAC,GADtE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAH,MAAMI,IAAAA,4BAAa,EAACJ;IAEpB,MAAMK,SAAS,MAAMC,IAAAA,eAAU,EAACC,mCAAwB,EAAEP;IAC1D,MAAMQ,UAAUC,aAAI,CAACC,IAAI,CAACV,KAAKK,OAAOG,OAAO,IAAI;IACjD,MAAMG,aAAaF,aAAI,CAACC,IAAI,CAACF,SAAS;IAEtC,IAAII;IACJ,IAAI;QACFA,SAASC,KAAKC,KAAK,CAACC,WAAE,CAACC,YAAY,CAACL,YAAY;IAClD,EAAE,OAAOM,KAAU;QACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;YACzB,uCAAuC;YACvCpB,QAAQqB,IAAI,CAAC;QACf;QACA,MAAMF;IACR;IAEA,MAAMG,YAAY,IAAIC,kBAAS,CAAC;QAAEb;IAAQ;IAC1C,MAAMY,UAAUE,MAAM,CAACV;IACvB,MAAMQ,UAAUG,KAAK;IAErB,yCAAyC;IACzCR,WAAE,CAACS,UAAU,CAACb;IACdb,QAAQqB,IAAI,CAAC;AACf,CAAA"}
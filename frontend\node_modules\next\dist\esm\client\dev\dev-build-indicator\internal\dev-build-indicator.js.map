{"version": 3, "sources": ["../../../../../src/client/dev/dev-build-indicator/internal/dev-build-indicator.ts"], "sourcesContent": ["import { initialize } from './initialize'\n\nconst NOOP = () => {}\n\nexport const devBuildIndicator = {\n  /** Shows build indicator when Next.js is compiling. Requires initialize() first. */\n  show: NOOP,\n  /** Hides build indicator when Next.js finishes compiling. Requires initialize() first. */\n  hide: NOOP,\n  /** Sets up the build indicator UI component. Call this before using show/hide. */\n  initialize,\n}\n"], "names": ["initialize", "NOOP", "devBuildIndicator", "show", "hide"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAc;AAEzC,MAAMC,OAAO,KAAO;AAEpB,OAAO,MAAMC,oBAAoB;IAC/B,kFAAkF,GAClFC,MAAMF;IACN,wFAAwF,GACxFG,MAAMH;IACN,gFAAgF,GAChFD;AACF,EAAC"}
{"version": 3, "sources": ["../../../../src/client/components/errors/enqueue-client-error.ts"], "sourcesContent": ["// Dedupe the two consecutive errors: If the previous one is same as current one, ignore the current one.\nexport function enqueueConsecutiveDedupedError(\n  queue: Array<Error>,\n  error: Error\n) {\n  const previousError = queue[queue.length - 1]\n  // Compare the error stack to dedupe the consecutive errors\n  if (previousError && previousError.stack === error.stack) {\n    return\n  }\n  queue.push(error)\n}\n"], "names": ["enqueueConsecutiveDedupedError", "queue", "error", "previousError", "length", "stack", "push"], "mappings": "AAAA,yGAAyG;AACzG,OAAO,SAASA,+BACdC,KAAmB,EACnBC,KAAY;IAEZ,MAAMC,gBAAgBF,KAAK,CAACA,MAAMG,MAAM,GAAG,EAAE;IAC7C,2DAA2D;IAC3D,IAAID,iBAAiBA,cAAcE,KAAK,KAAKH,MAAMG,KAAK,EAAE;QACxD;IACF;IACAJ,MAAMK,IAAI,CAACJ;AACb"}